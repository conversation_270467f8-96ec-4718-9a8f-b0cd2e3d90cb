/* ========================================
   Clash Verge Rev 优化主题 - 浅色/深色双模式
   ======================================== */

/* 全屏背景图片 */
html, body, #root {
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }
  
  /* 背景图片设置 - 使用渐变背景替代远程图片 */
  
  /* 使用渐变背景确保兼容性 */
  body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
  }
  
  /* 备用渐变背景 */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -2;
    pointer-events: none;
    opacity: 0.3;
  }
  
  /* 半透明覆盖层确保内容可读性 */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
    pointer-events: none;
  }
  
  /* 主题模式检测和变量定义 */
  :root {
    /* 浅色模式变量 */
    --primary-color: #1976d2;
    --primary-light: #42a5f5;
    --primary-dark: #1565c0;
    --secondary-color: #dc004e;
    --secondary-light: #ff5983;
    --secondary-dark: #9a0036;
    
    --background-primary: rgba(255, 255, 255, 0.95);
    --background-secondary: rgba(248, 249, 250, 0.9);
    --background-tertiary: rgba(255, 255, 255, 0.8);
    --background-glass: rgba(255, 255, 255, 0.15);
    --background-card: rgba(255, 255, 255, 0.85);
    --background-overlay: rgba(0, 0, 0, 0.1);
    
    --text-primary: rgba(33, 37, 41, 0.95);
    --text-secondary: rgba(73, 80, 87, 0.8);
    --text-tertiary: rgba(108, 117, 125, 0.7);
    --text-inverse: rgba(255, 255, 255, 0.95);
    
    --border-color: rgba(0, 0, 0, 0.12);
    --border-light: rgba(0, 0, 0, 0.08);
    --border-focus: rgba(25, 118, 210, 0.5);
    
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    
    --blur-light: blur(8px);
    --blur-medium: blur(12px);
    --blur-heavy: blur(20px);
    
    /* 布局变量 */
    --base-spacing: 1.5rem;
    --grid-gap: 1.2rem;
    --card-min-width: 280px;
    --sidebar-width: 280px;
    --header-height: 64px;
    --border-radius: 12px;
    --border-radius-small: 8px;
    --border-radius-large: 16px;
    
    /* 响应式断点 */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 768px;
    --breakpoint-md: 1024px;
    --breakpoint-lg: 1200px;
    --breakpoint-xl: 1440px;
    
    /* 动画变量 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* 深色模式变量 */
  [data-theme="dark"],
  .dark-mode {
    --primary-color: #90caf9;
    --primary-light: #bbdefb;
    --primary-dark: #64b5f6;
    --secondary-color: #f48fb1;
    --secondary-light: #f8bbd9;
    --secondary-dark: #f06292;
  
    --background-primary: rgba(18, 18, 18, 0.95);
    --background-secondary: rgba(33, 33, 33, 0.9);
    --background-tertiary: rgba(48, 48, 48, 0.8);
    --background-glass: rgba(255, 255, 255, 0.08);
    --background-card: rgba(33, 33, 33, 0.85);
    --background-overlay: rgba(255, 255, 255, 0.05);
  
    --text-primary: rgba(255, 255, 255, 1);
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-tertiary: rgba(255, 255, 255, 0.7);
    --text-inverse: rgba(33, 37, 41, 0.95);
  
    --border-color: rgba(255, 255, 255, 0.12);
    --border-light: rgba(255, 255, 255, 0.08);
    --border-focus: rgba(144, 202, 249, 0.5);
  
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);
  }
  
  /* 系统深色模式自动检测 */
  @media (prefers-color-scheme: dark) {
    :root:not([data-theme="light"]):not(.light-mode) {
      --primary-color: #90caf9;
      --primary-light: #bbdefb;
      --primary-dark: #64b5f6;
      --secondary-color: #f48fb1;
      --secondary-light: #f8bbd9;
      --secondary-dark: #f06292;
  
      --background-primary: rgba(18, 18, 18, 0.95);
      --background-secondary: rgba(33, 33, 33, 0.9);
      --background-tertiary: rgba(48, 48, 48, 0.8);
      --background-glass: rgba(255, 255, 255, 0.08);
      --background-card: rgba(33, 33, 33, 0.85);
      --background-overlay: rgba(255, 255, 255, 0.05);
  
      --text-primary: rgba(255, 255, 255, 1);
      --text-secondary: rgba(255, 255, 255, 0.9);
      --text-tertiary: rgba(255, 255, 255, 0.7);
      --text-inverse: rgba(33, 37, 41, 0.95);
  
      --border-color: rgba(255, 255, 255, 0.12);
      --border-light: rgba(255, 255, 255, 0.08);
      --border-focus: rgba(144, 202, 249, 0.5);
  
      --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
      --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
      --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);
    }
  }
  
  /* 主题切换动画 - 已优化性能 */
  * {
    transition: background-color var(--transition-fast),
                color var(--transition-fast);
  }
  
  /* 全局重置和基础样式 */
  * {
    box-sizing: border-box;
  }
  
  html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif, 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体';
    background: transparent;
    color: var(--text-primary);
    overflow-x: hidden;
  }
  
  /* 滚动条美化 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: background var(--transition-normal);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
  }
  
  /* 选择文本样式 */
  ::selection {
    background: var(--primary-color);
    color: var(--text-inverse);
  }
  
  /* ========================================
     主要布局组件
     ======================================== */
  
  /* 应用主容器 */
  #root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: transparent;
  }
  
  /* 主布局容器 */
  .layout, .app-layout {
    display: flex;
    flex: 1;
    min-height: calc(100vh - var(--header-height));
    background: transparent;
  }
  
  /* 左侧导航栏 */
  .layout__left, .sidebar, .nav-sidebar {
    flex: 0 0 var(--sidebar-width);
    background: var(--background-glass);
    backdrop-filter: var(--blur-medium);
    border-right: 1px solid var(--border-light);
    padding: var(--base-spacing);
    min-width: 240px;
    max-width: 320px;
    box-shadow: var(--shadow-light);
    position: relative;
    z-index: 10;
  }
  
  /* 主内容区域 */
  .layout__right, .main-content {
    flex: 1;
    background: var(--background-overlay);
    backdrop-filter: var(--blur-light);
    padding: var(--base-spacing);
    overflow-y: auto;
    position: relative;
  }
  
  /* 头部导航 */
  .header, .app-header {
    height: var(--header-height);
    background: var(--background-glass);
    backdrop-filter: var(--blur-medium);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    padding: 0 var(--base-spacing);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 100;
  }
  
  /* ========================================
     响应式网格系统
     ======================================== */
  
  /* 响应式网格容器 */
  .responsive-grid, .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
    gap: var(--grid-gap);
    padding: var(--grid-gap);
    width: 100%;
    align-items: start;
  }
  
  /* 网格项目 */
  .grid-item {
    display: flex;
    flex-direction: column;
    min-height: 0;
  }
  
  /* 菜单系统网格 */
  .the-menu, .menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--base-spacing);
    margin: var(--base-spacing) 0;
  }
  
  /* ========================================
     卡片组件系统
     ======================================== */
  
  /* 基础卡片 - 删除阴影和外围悬停效果 */
  .grid-card, .card, .MuiCard-root {
    background: var(--background-card) !important;
    border-radius: var(--border-radius) !important;
    backdrop-filter: var(--blur-medium);
    border: 1px solid var(--border-light);
    transition: background-color var(--transition-fast) !important;
    overflow: hidden;
    break-inside: avoid;
    position: relative;
  }
  
  /* 卡片外围悬停效果已删除 */
  
  /* 卡片选中状态和悬停效果已删除 - 使用应用默认样式 */
  
  /* 卡片内容 */
  .MuiCardContent-root {
    padding: var(--base-spacing) !important;
    color: var(--text-primary) !important;
  }
  
  /* 卡片标题 */
  .MuiCardHeader-root {
    background: var(--background-overlay);
    border-bottom: 1px solid var(--border-light);
    padding: calc(var(--base-spacing) * 0.75) var(--base-spacing) !important;
  }
  
  .MuiCardHeader-title {
    color: var(--text-primary) !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
  }
  
  .MuiCardHeader-subheader {
    color: var(--text-secondary) !important;
    font-size: 0.875rem !important;
  }
  
  /* 紧凑卡片 */
  .card-compact {
    min-height: 120px;
  }
  
  /* 扩展卡片 */
  .card-expanded {
    min-height: 200px;
  }
  
  /* 特色卡片 */
  .card-featured {
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-medium);
  }
  
  .card-featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  }
  
  /* ========================================
     Material-UI 组件优化
     ======================================== */
  
  /* 按钮组件 */
  .MuiButton-root, .MuiButtonBase-root {
    background: var(--background-glass) !important;
    border: 1px solid var(--border-light) !important;
    color: var(--text-primary) !important;
    backdrop-filter: var(--blur-light);
    border-radius: var(--border-radius-small) !important;
    transition: background-color var(--transition-fast) !important;
    font-weight: 500 !important;
    text-transform: none !important;
    box-shadow: var(--shadow-light) !important;
  }
  
  .MuiButton-root:hover, .MuiButtonBase-root:hover {
    background: var(--primary-color) !important;
    color: var(--text-inverse) !important;
    border-color: var(--primary-color) !important;
  }
  
  /* 主要按钮 */
  .MuiButton-contained {
    background: var(--primary-color) !important;
    color: var(--text-inverse) !important;
  }
  
  .MuiButton-contained:hover {
    background: var(--primary-dark) !important;
  }
  
  /* 次要按钮 */
  .MuiButton-outlined {
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
  }
  
  .MuiButton-outlined:hover {
    background: var(--primary-color) !important;
    color: var(--text-inverse) !important;
  }
  
  /* 文本按钮 */
  .MuiButton-text {
    color: var(--primary-color) !important;
    background: transparent !important;
    border: none !important;
  }
  
  .MuiButton-text:hover {
    background: var(--background-overlay) !important;
  }
  
  /* 输入框组件 */
  .MuiTextField-root, .MuiOutlinedInput-root {
    background: var(--background-card) !important;
    border-radius: var(--border-radius-small) !important;
    backdrop-filter: var(--blur-light);
  }
  
  .MuiOutlinedInput-notchedOutline {
    border-color: var(--border-color) !important;
    transition: border-color var(--transition-normal) !important;
  }
  
  .MuiOutlinedInput-root:hover {
    background: rgba(33, 150, 243, 0.02) !important;
    color: var(--text-primary) !important;
  }
  
  .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(33, 150, 243, 0.3) !important;
  }
  
  .MuiOutlinedInput-root:hover input {
    color: var(--text-primary) !important;
  }
  
  .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-color: var(--primary-color) !important;
    border-width: 2px !important;
    box-shadow: 0 0 0 3px var(--border-focus) !important;
  }
  
  .MuiInputBase-input {
    color: var(--text-primary) !important;
  }
  
  .MuiInputLabel-root {
    color: var(--text-secondary) !important;
  }
  
  .MuiInputLabel-root.Mui-focused {
    color: var(--primary-color) !important;
  }
  
  /* 开关组件 - 统一样式，不受主题影响 */
  /* 开关基础样式保持默认 */
  
  .MuiSwitch-track {
    background-color: rgba(0, 0, 0, 0.38) !important;
    opacity: 1 !important;
  }
  
  .MuiSwitch-thumb {
    background-color: #fafafa !important;
    color: #fafafa !important;
  }
  
  /* 开启状态 */
  .MuiSwitch-root .Mui-checked .MuiSwitch-thumb {
    background-color: #fff !important;
    color: #fff !important;
  }
  
  .MuiSwitch-root .Mui-checked + .MuiSwitch-track {
    background-color: #1976d2 !important;
    opacity: 1 !important;
  }
  
  /* 确保在所有主题模式下都保持一致 */
  [data-theme="dark"] .MuiSwitch-track,
  [data-theme="light"] .MuiSwitch-track,
  .dark-mode .MuiSwitch-track,
  .light-mode .MuiSwitch-track {
    background-color: rgba(0, 0, 0, 0.38) !important;
  }
  
  [data-theme="dark"] .MuiSwitch-root .Mui-checked + .MuiSwitch-track,
  [data-theme="light"] .MuiSwitch-root .Mui-checked + .MuiSwitch-track,
  .dark-mode .MuiSwitch-root .Mui-checked + .MuiSwitch-track,
  .light-mode .MuiSwitch-root .Mui-checked + .MuiSwitch-track {
    background-color: #1976d2 !important;
  }
  
  [data-theme="dark"] .MuiSwitch-thumb,
  [data-theme="light"] .MuiSwitch-thumb,
  .dark-mode .MuiSwitch-thumb,
  .light-mode .MuiSwitch-thumb {
    background-color: #fafafa !important;
  }
  
  [data-theme="dark"] .MuiSwitch-root .Mui-checked .MuiSwitch-thumb,
  [data-theme="light"] .MuiSwitch-root .Mui-checked .MuiSwitch-thumb,
  .dark-mode .MuiSwitch-root .Mui-checked .MuiSwitch-thumb,
  .light-mode .MuiSwitch-root .Mui-checked .MuiSwitch-thumb {
    background-color: #fff !important;
  }
  
  /* 选择器组件 */
  .MuiSelect-root {
    background: var(--background-card) !important;
    color: var(--text-primary) !important;
  }
  
  .MuiMenuItem-root {
    background: var(--background-card) !important;
    color: var(--text-primary) !important;
    transition: background-color var(--transition-fast) !important;
  }
  
  .MuiMenuItem-root:hover {
    background: var(--background-overlay) !important;
  }
  
  .MuiMenuItem-root.Mui-selected {
    background: var(--primary-color) !important;
    color: var(--text-inverse) !important;
  }
  
  /* 标签页组件 */
  .MuiTabs-root {
    background: var(--background-glass);
    backdrop-filter: var(--blur-light);
    border-bottom: 1px solid var(--border-light);
  }
  
  .MuiTab-root {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    text-transform: none !important;
    transition: color var(--transition-fast) !important;
  }
  
  .MuiTab-root.Mui-selected {
    color: var(--primary-color) !important;
  }
  
  .MuiTabs-indicator {
    background-color: var(--primary-color) !important;
    height: 3px !important;
    border-radius: 2px !important;
  }
  
  /* ========================================
     文字系统和排版
     ======================================== */
  
  /* 基础文字样式 */
  .MuiTypography-root {
    color: var(--text-primary) !important;
    line-height: 1.6 !important;
  }
  
  .MuiTypography-h1, .MuiTypography-h2, .MuiTypography-h3,
  .MuiTypography-h4, .MuiTypography-h5, .MuiTypography-h6 {
    font-weight: 600 !important;
    margin-bottom: 0.5em !important;
    color: var(--text-primary) !important;
  }
  
  .MuiTypography-body1 {
    color: var(--text-primary) !important;
    font-size: 1rem !important;
  }
  
  .MuiTypography-body2 {
    color: var(--text-secondary) !important;
    font-size: 0.875rem !important;
  }
  
  .MuiTypography-caption {
    color: var(--text-tertiary) !important;
    font-size: 0.75rem !important;
  }
  
  /* 链接样式 */
  a, .MuiLink-root {
    color: var(--primary-color) !important;
    text-decoration: none !important;
    transition: color var(--transition-fast) !important;
  }
  
  a:hover, .MuiLink-root:hover {
    color: var(--primary-dark) !important;
    text-decoration: underline !important;
  }
  
  /* ========================================
     列表和导航组件
     ======================================== */
  
  /* 列表组件 */
  .MuiList-root {
    background: transparent !important;
    padding: 0 !important;
  }
  
  .MuiListItem-root {
    background: transparent !important;
    border-radius: var(--border-radius-small) !important;
    margin-bottom: 4px !important;
    transition: background-color var(--transition-fast) !important;
  }
  
  /* 列表项悬停和选中状态已删除 - 使用应用默认样式 */
  
  .MuiListItem-root.Mui-selected {
    background: var(--primary-color) !important;
    color: var(--text-inverse) !important;
  }
  
  .MuiListItem-root.Mui-selected .MuiListItemText-primary,
  .MuiListItem-root.Mui-selected .MuiListItemText-secondary,
  .MuiListItem-root.Mui-selected .MuiListItemIcon-root {
    color: var(--text-inverse) !important;
  }
  
  .MuiListItemText-primary {
    color: var(--text-primary) !important;
    font-weight: 500 !important;
  }
  
  .MuiListItemText-secondary {
    color: var(--text-secondary) !important;
  }
  
  .MuiListItemIcon-root {
    color: var(--text-secondary) !important;
    min-width: 40px !important;
  }
  
  /* 分割线 */
  .MuiDivider-root {
    background-color: var(--border-light) !important;
    margin: var(--base-spacing) 0 !important;
  }
  
  /* ========================================
     对话框和弹出层
     ======================================== */
  
  /* 对话框 */
  .MuiDialog-paper {
    background: var(--background-card) !important;
    backdrop-filter: var(--blur-heavy);
    border-radius: var(--border-radius-large) !important;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-heavy) !important;
  }
  
  .MuiDialogTitle-root {
    background: var(--background-overlay);
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary) !important;
    font-weight: 600 !important;
  }
  
  .MuiDialogContent-root {
    color: var(--text-primary) !important;
    padding: var(--base-spacing) !important;
  }
  
  .MuiDialogActions-root {
    background: var(--background-overlay);
    border-top: 1px solid var(--border-light);
    padding: calc(var(--base-spacing) * 0.75) var(--base-spacing) !important;
  }
  
  /* 工具提示 */
  .MuiTooltip-tooltip {
    background: var(--background-card) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-light);
    backdrop-filter: var(--blur-medium);
    box-shadow: var(--shadow-medium) !important;
    font-size: 0.75rem !important;
  }
  
  /* 菜单弹出层 */
  .MuiMenu-paper, .MuiPopover-paper {
    background: var(--background-card) !important;
    backdrop-filter: var(--blur-heavy);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-heavy) !important;
  }
  
  /* ========================================
     进度和加载组件
     ======================================== */
  
  /* 进度条 */
  .MuiLinearProgress-root {
    background-color: var(--border-light) !important;
    border-radius: 4px !important;
    height: 6px !important;
  }
  
  .MuiLinearProgress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)) !important;
    border-radius: 4px !important;
  }
  
  /* 圆形进度 */
  .MuiCircularProgress-root {
    color: var(--primary-color) !important;
  }
  
  /* 骨架屏 */
  .MuiSkeleton-root {
    background-color: var(--background-overlay) !important;
  }
  
  .MuiSkeleton-wave::after {
    background: linear-gradient(90deg, transparent, var(--background-glass), transparent) !important;
  }
  
  /* ========================================
     响应式设计断点
     ======================================== */
  
  /* 超小屏幕 (手机竖屏) */
  @media (max-width: 480px) {
    :root {
      --base-spacing: 1rem;
      --grid-gap: 0.8rem;
      --sidebar-width: 100%;
      --card-min-width: 100%;
    }
  
    .layout, .app-layout {
      flex-direction: column;
    }
  
    .layout__left, .sidebar {
      flex: none;
      width: 100%;
      min-width: auto;
      max-width: none;
      border-right: none;
      border-bottom: 1px solid var(--border-light);
    }
  
    .responsive-grid, .grid-container {
      grid-template-columns: 1fr;
      gap: var(--grid-gap);
      padding: var(--grid-gap);
    }
  
    .header, .app-header {
      padding: 0 1rem;
    }
  
    .MuiDialog-paper {
      margin: 1rem;
      width: calc(100% - 2rem);
      max-width: none;
    }
  }
  
  /* 小屏幕 (手机横屏/小平板) */
  @media (max-width: 768px) {
    :root {
      --base-spacing: 1.25rem;
      --grid-gap: 1rem;
      --sidebar-width: 240px;
      --card-min-width: 240px;
    }
  
    .responsive-grid, .grid-container {
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
  
    .layout__left, .sidebar {
      flex: 0 0 240px;
      min-width: 200px;
    }
  }
  
  /* 中等屏幕 (平板) */
  @media (max-width: 1024px) {
    :root {
      --card-min-width: 260px;
    }
  
    .responsive-grid, .grid-container {
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    }
  }
  
  /* 大屏幕优化 */
  @media (min-width: 1200px) {
    :root {
      --card-min-width: 320px;
      --sidebar-width: 320px;
    }
  
    .responsive-grid, .grid-container {
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
  
    .layout__left, .sidebar {
      flex: 0 0 320px;
    }
  }
  
  /* 超大屏幕优化 */
  @media (min-width: 1440px) {
    :root {
      --base-spacing: 2rem;
      --grid-gap: 1.5rem;
      --card-min-width: 360px;
    }
  
    .responsive-grid, .grid-container {
      grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
      gap: 1.5rem;
    }
  }
  
  /* ========================================
     特殊效果和动画 (已优化性能)
     ======================================== */
  
  /* 仅保留必要的加载旋转动画 */
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .spin-animation {
    animation: spin 1s linear infinite;
  }
  
  /* 简单的渐入效果 */
  .fade-in {
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .fade-in.visible {
    opacity: 1;
  }
  
  /* ========================================
     主题切换按钮
     ======================================== */
  
  .theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--background-glass);
    backdrop-filter: var(--blur-medium);
    border: 1px solid var(--border-light);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
    box-shadow: var(--shadow-light);
    z-index: 1000;
  }
  
  .theme-toggle:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
  }
  
  /* ========================================
     实用工具类
     ======================================== */
  
  /* 间距工具 */
  .m-0 { margin: 0 !important; }
  .m-1 { margin: 0.5rem !important; }
  .m-2 { margin: 1rem !important; }
  .m-3 { margin: 1.5rem !important; }
  .m-4 { margin: 2rem !important; }
  
  .p-0 { padding: 0 !important; }
  .p-1 { padding: 0.5rem !important; }
  .p-2 { padding: 1rem !important; }
  .p-3 { padding: 1.5rem !important; }
  .p-4 { padding: 2rem !important; }
  
  /* 文本对齐 */
  .text-left { text-align: left !important; }
  .text-center { text-align: center !important; }
  .text-right { text-align: right !important; }
  
  /* 显示控制 */
  .d-none { display: none !important; }
  .d-block { display: block !important; }
  .d-flex { display: flex !important; }
  .d-grid { display: grid !important; }
  
  /* 弹性布局 */
  .flex-center {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  .flex-between {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
  }
  
  /* 圆角工具 */
  .rounded { border-radius: var(--border-radius) !important; }
  .rounded-sm { border-radius: var(--border-radius-small) !important; }
  .rounded-lg { border-radius: var(--border-radius-large) !important; }
  .rounded-full { border-radius: 50% !important; }
  
  /* 阴影工具 */
  .shadow-light { box-shadow: var(--shadow-light) !important; }
  .shadow-medium { box-shadow: var(--shadow-medium) !important; }
  .shadow-heavy { box-shadow: var(--shadow-heavy) !important; }
  
  /* 毛玻璃效果 */
  .glass-light { backdrop-filter: var(--blur-light) !important; }
  .glass-medium { backdrop-filter: var(--blur-medium) !important; }
  .glass-heavy { backdrop-filter: var(--blur-heavy) !important; }
  
  /* ========================================
     打印样式
     ======================================== */
  
  @media print {
    html::before, body::before, body::after {
      display: none;
    }
  
    html, body, #root, .app {
      background: none !important;
    }
  
    .theme-toggle,
    .MuiButton-root,
    .sidebar,
    .layout__left {
      display: none !important;
    }
  
    .layout__right,
    .main-content {
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      background: white !important;
      color: black !important;
    }
  
    .grid-card,
    .card,
    .MuiCard-root {
      background: white !important;
      border: 1px solid #ccc !important;
      box-shadow: none !important;
      break-inside: avoid;
    }
  }
  